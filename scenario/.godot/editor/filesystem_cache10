fc8a56933c4b1c8d796fdb8f7a9f9475
::res://::1751310029
icon.svg::CompressedTexture2D/CompressedTexture2D::4677646507598207582::1751302641::1751302887::1::::<><><>0<>0<>c8bae945c7faa46f6129fad7ddbcbcf7<>res://.godot/imported/icon.svg-218a8f2b3041327d8a5756f3a245f83b.ctex::
MAIN_MENU_README.md::TextFile/TextFile::-1::1751309238::0::1::::<><><>0<>0<><>::
package-lock.json::JSON/JSON::-1::1751306139::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::1751306139::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::1751302671::0::1::::<><><>0<>0<><>::
README_NODEJS.md::TextFile/TextFile::-1::1751303851::0::1::::<><><>0<>0<><>::
SCENARIO_PROJECT_GUIDE.md::TextFile/TextFile::-1::1751302848::0::1::::<><><>0<>0<><>::
test_menu.gd::GDScript/GDScript::8535680172591216493::1751309712::0::1::::<>SceneTree<><>0<>0<><>::
validate_menu.gd::GDScript::4993216221681861224::1751309981::0::1::::<>Control<><>0<>0<><>::
::res://assets/::1751309036
::res://assets/generated/::1751309036
dark_anime_van_helsing.png::CompressedTexture2D/CompressedTexture2D::2437319207021337659::1751307715::1751307990::1::::<><><>0<>0<>844af3176ade8a5864d443c3d860265f<>res://.godot/imported/dark_anime_van_helsing.png-1e64ea88c4b2a8cc4f52afb3cd809c3f.ctex::
gothic_button.png::CompressedTexture2D/CompressedTexture2D::4409856321195262239::1751307057::**********::0::::<><><>0<>0<>8d5d0247256f244ca78b715068d87afe<>::
main_menu_background.png::CompressedTexture2D/CompressedTexture2D::226649138348948613::1751307039::**********::0::::<><><>0<>0<>2845250b13a0ac789e7bad4cd5b875ec<>::
menu_background_main.png::CompressedTexture2D/CompressedTexture2D::8858270826719731749::1751308418::**********::0::::<><><>0<>0<>cbd9ba31d514154ada553680fd18c314<>::
menu_button_kapitoly.png::CompressedTexture2D/CompressedTexture2D::8086473827272039267::1751308846::**********::0::::<><><>0<>0<>abff43e0386b83ceca567c054f8c2d2b<>::
menu_button_nastavenia.png::CompressedTexture2D/CompressedTexture2D::7086143032777923367::1751308853::**********::0::::<><><>0<>0<>dd35843712d48b646f1d36571a6d56aa<>::
menu_button_nova_hra.png::CompressedTexture2D/CompressedTexture2D::2069574301533198491::1751308838::**********::0::::<><><>0<>0<>a8939eb0ead8946eece4fe207e60d50f<>::
menu_button_o_hre.png::CompressedTexture2D/CompressedTexture2D::7889484775116857137::1751308861::**********::0::::<><><>0<>0<>199d05a1899c635539199424e87fd59c<>::
menu_decoration_horizontal.png::CompressedTexture2D/CompressedTexture2D::1758264388174115224::1751308869::**********::0::::<><><>0<>0<>2178243df6288ec19b74bb72bb0eb96c<>::
menu_panel_main.png::CompressedTexture2D/CompressedTexture2D::140548242636823173::1751308732::**********::0::::<><><>0<>0<>1614cd8a866a95042344d656182348b6<>::
menu_title_frame.png::CompressedTexture2D/CompressedTexture2D::6458970337144135738::1751308787::**********::0::::<><><>0<>0<>a5e590cc2bd0c77effba50b3b0b31628<>::
menu_van_helsing_portrait.png::CompressedTexture2D/CompressedTexture2D::2374450546598370556::1751308780::**********::1::::<><><>0<>0<>189544314923781c67acd49c97073454<>res://.godot/imported/menu_van_helsing_portrait.png-8f5e2078fa3f0787b3d01e7dd313c1e8.ctex::
title_logo.png::CompressedTexture2D/CompressedTexture2D::2937465670245385561::**********::**********::0::::<><><>0<>0<>157b3764949acb5693a6c5351a1490e0<>::
ui_gothic_menu_button.png::CompressedTexture2D/CompressedTexture2D::5543128498889112861::**********::**********::1::::<><><>0<>0<>9b80be1d022bc719263d2a10e683e7f2<>res://.godot/imported/ui_gothic_menu_button.png-5802bfd344ef72d48e4e726eadc5459e.ctex::
ui_health_bar_frame.png::CompressedTexture2D/CompressedTexture2D::1933071746565359717::**********::**********::1::::<><><>0<>0<>073c93487b2d3418933294f8493474f9<>res://.godot/imported/ui_health_bar_frame.png-333a1acec2570d8e08f1d9d3c341d893.ctex::
ui_inventory_panel.png::CompressedTexture2D/CompressedTexture2D::7015745317750514155::**********::**********::1::::<><><>0<>0<>965083c5a7c08346d4eed8db456dfc58<>res://.godot/imported/ui_inventory_panel.png-a7a4a38fc60688df3f80211635b467e6.ctex::
ui_vampire_icon.png::CompressedTexture2D/CompressedTexture2D::6578461448510815280::**********::**********::1::::<><><>0<>0<>bf94dba6480cf3c33066efe00c489e53<>res://.godot/imported/ui_vampire_icon.png-8806b807d31e19d9ada74c1d0c2f4fbd.ctex::
van_helsing_portrait.png::CompressedTexture2D/CompressedTexture2D::68686384804849550::**********::**********::0::::<><><>0<>0<>dab41fab79567a8fc21303f505bcbe92<>::
::res://node_modules/::**********
::res://node_modules/base64-js/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/bl/::**********
LICENSE.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/bl/test/::**********
::res://node_modules/buffer/::**********
AUTHORS.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/canvas/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
Readme.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/canvas/build/::**********
::res://node_modules/canvas/build/Release/::**********
::res://node_modules/canvas/lib/::**********
::res://node_modules/canvas/src/::**********
::res://node_modules/canvas/src/backend/::**********
::res://node_modules/canvas/src/bmp/::**********
LICENSE.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/canvas/util/::**********
::res://node_modules/chownr/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/data-uri-to-buffer/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/data-uri-to-buffer/dist/::**********
::res://node_modules/data-uri-to-buffer/src/::**********
::res://node_modules/decompress-response/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
readme.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/deep-extend/::**********
CHANGELOG.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/deep-extend/lib/::**********
::res://node_modules/detect-libc/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/detect-libc/lib/::**********
::res://node_modules/dotenv/::**********
CHANGELOG.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README-es.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
SECURITY.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/dotenv/lib/::**********
::res://node_modules/end-of-stream/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/expand-template/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/fetch-blob/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/formdata-polyfill/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/fs-constants/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/github-from-package/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/github-from-package/example/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/github-from-package/test/::**********
a.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
b.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
c.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
d.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
e.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/ieee754/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/inherits/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/ini/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/mimic-response/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
readme.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/minimist/::**********
CHANGELOG.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/minimist/example/::**********
::res://node_modules/minimist/test/::**********
::res://node_modules/mkdirp-classic/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/napi-build-utils/::**********
index.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-abi/::**********
abi_registry.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-addon-api/::**********
LICENSE.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package-support.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-addon-api/tools/::**********
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-domexception/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-fetch/::**********
LICENSE.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-fetch/@types/::**********
::res://node_modules/node-fetch/src/::**********
::res://node_modules/node-fetch/src/errors/::**********
::res://node_modules/node-fetch/src/utils/::**********
::res://node_modules/once/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/prebuild-install/::**********
CHANGELOG.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
CONTRIBUTING.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
help.txt::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/pump/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
SECURITY.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/rc/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/rc/lib/::**********
::res://node_modules/rc/test/::**********
::res://node_modules/readable-stream/::**********
CONTRIBUTING.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
GOVERNANCE.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/readable-stream/lib/::**********
::res://node_modules/readable-stream/lib/internal/::**********
::res://node_modules/readable-stream/lib/internal/streams/::**********
::res://node_modules/safe-buffer/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/semver/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/semver/bin/::**********
::res://node_modules/semver/classes/::**********
::res://node_modules/semver/functions/::**********
::res://node_modules/semver/internal/::**********
::res://node_modules/semver/ranges/::**********
::res://node_modules/simple-concat/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/simple-concat/test/::**********
::res://node_modules/simple-get/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/string_decoder/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/string_decoder/lib/::**********
::res://node_modules/strip-json-comments/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
readme.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/tar-fs/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/tar-fs/test/::**********
::res://node_modules/tar-fs/test/fixtures/::**********
::res://node_modules/tar-fs/test/fixtures/a/::1751306207
hello.txt::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/tar-fs/test/fixtures/b/::1751306207
::res://node_modules/tar-fs/test/fixtures/b/a/::1751306207
test.txt::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/tar-fs/test/fixtures/d/::1751306207
::res://node_modules/tar-fs/test/fixtures/d/sub-dir/::1751306207
::res://node_modules/tar-fs/test/fixtures/d/sub-files/::1751306207
::res://node_modules/tar-fs/test/fixtures/e/::1751306207
::res://node_modules/tar-fs/test/fixtures/e/directory/::1751306207
::res://node_modules/tar-stream/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/tunnel-agent/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/util-deprecate/::**********
History.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::1751303858::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/dist/::**********
::res://node_modules/web-streams-polyfill/dist/types/::**********
tsdoc-metadata.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/dist/types/ts3.6/::**********
::res://node_modules/web-streams-polyfill/es6/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/es2018/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/ponyfill/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/ponyfill/es6/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/ponyfill/es2018/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/wrappy/::**********
package.json::JSON/JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://scenes/::1751309996
AssetGenerator.tscn::PackedScene/PackedScene::-1::1751302754::0::1::::<><><>0<>0<><>::res://scripts/AssetGenerator.gd
MainMenu.tscn::PackedScene::4070188531361412054::1751309950::0::1::::<><><>0<>0<><>::uid://dl1a2ateu1dmw::::res://scripts/MainMenu.gd<>uid://83g4a0si4hwm::::res://assets/generated/menu_van_helsing_portrait.png
TestMenu.tscn::PackedScene::-1::1751309991::0::1::::<><><>0<>0<><>::uid://bx8vn7qkqxqxq::::res://scenes/MainMenu.tscn
::res://scripts/::1751309647
AssetGenerator.gd::GDScript/GDScript::6733254567586422308::1751302726::0::1::::<>Control<><>0<>0<><>::
MainMenu.gd::GDScript/GDScript::7985061398699423882::1751309642::0::1::::<>Control<><>0<>0<><>::
ScenarioAPI.gd::GDScript/GDScript::8472668313112885933::1751302689::0::1::::<>Node<><>0<>0<><>::
