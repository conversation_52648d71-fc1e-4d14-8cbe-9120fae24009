fc8a56933c4b1c8d796fdb8f7a9f9475
::res://::1751306154
icon.svg::CompressedTexture2D/CompressedTexture2D::4677646507598207582::1751302641::1751302887::1::::<><><>0<>0<>c8bae945c7faa46f6129fad7ddbcbcf7<>res://.godot/imported/icon.svg-218a8f2b3041327d8a5756f3a245f83b.ctex::
package-lock.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile/TextFile::-1::1751302671::0::1::::<><><>0<>0<><>::
README_NODEJS.md::TextFile::-1::1751303851::0::1::::<><><>0<>0<><>::
SCENARIO_PROJECT_GUIDE.md::TextFile/TextFile::-1::1751302848::0::1::::<><><>0<>0<><>::
::res://assets/::1751302649
::res://assets/generated/::**********
gothic_button.png::CompressedTexture2D::4409856321195262239::**********::**********::1::::<><><>0<>0<>63c7d4de80cefd984f652cfa27eb8351<>res://.godot/imported/gothic_button.png-51eb5bbb65157505a4adb07acc4975cd.ctex::
main_menu_background.png::CompressedTexture2D::226649138348948613::**********::**********::1::::<><><>0<>0<>307b1cd1ec25d918e7e8a19355c5c7fd<>res://.godot/imported/main_menu_background.png-b801ab04beb26a3ef413f966c9ee7426.ctex::
title_logo.png::CompressedTexture2D::2937465670245385561::**********::**********::1::::<><><>0<>0<>6b3a93f8bd3f1d146c1fd67bf982e300<>res://.godot/imported/title_logo.png-684941b615dc46cb5f539e4b6ca18026.ctex::
van_helsing_portrait.png::CompressedTexture2D::68686384804849550::**********::**********::1::::<><><>0<>0<>3495e0cdd8fb5bac2da8e312def7b249<>res://.godot/imported/van_helsing_portrait.png-0b956f8a6233597d93556c53a2d13dbb.ctex::
::res://node_modules/::**********
::res://node_modules/base64-js/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/bl/::**********
LICENSE.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/bl/test/::**********
::res://node_modules/buffer/::**********
AUTHORS.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/canvas/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
Readme.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/canvas/build/::**********
::res://node_modules/canvas/build/Release/::**********
::res://node_modules/canvas/lib/::**********
::res://node_modules/canvas/src/::**********
::res://node_modules/canvas/src/backend/::**********
::res://node_modules/canvas/src/bmp/::**********
LICENSE.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/canvas/util/::**********
::res://node_modules/chownr/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/data-uri-to-buffer/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/data-uri-to-buffer/dist/::**********
::res://node_modules/data-uri-to-buffer/src/::**********
::res://node_modules/decompress-response/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
readme.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/deep-extend/::**********
CHANGELOG.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/deep-extend/lib/::**********
::res://node_modules/detect-libc/::1751306154
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/detect-libc/lib/::**********
::res://node_modules/dotenv/::**********
CHANGELOG.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README-es.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
SECURITY.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/dotenv/lib/::**********
::res://node_modules/end-of-stream/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/expand-template/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/fetch-blob/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/formdata-polyfill/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/fs-constants/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/github-from-package/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/github-from-package/example/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/github-from-package/test/::**********
a.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
b.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
c.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
d.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
e.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/ieee754/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/inherits/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/ini/::1751306154
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/mimic-response/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
readme.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/minimist/::1751306154
CHANGELOG.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/minimist/example/::**********
::res://node_modules/minimist/test/::**********
::res://node_modules/mkdirp-classic/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/napi-build-utils/::**********
index.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-abi/::**********
abi_registry.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-addon-api/::**********
LICENSE.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package-support.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-addon-api/tools/::**********
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-domexception/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-fetch/::**********
LICENSE.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/node-fetch/@types/::**********
::res://node_modules/node-fetch/src/::**********
::res://node_modules/node-fetch/src/errors/::**********
::res://node_modules/node-fetch/src/utils/::**********
::res://node_modules/once/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/prebuild-install/::**********
CHANGELOG.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
CONTRIBUTING.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
help.txt::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/pump/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
SECURITY.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/rc/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/rc/lib/::**********
::res://node_modules/rc/test/::**********
::res://node_modules/readable-stream/::**********
CONTRIBUTING.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
GOVERNANCE.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/readable-stream/lib/::**********
::res://node_modules/readable-stream/lib/internal/::**********
::res://node_modules/readable-stream/lib/internal/streams/::**********
::res://node_modules/safe-buffer/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/semver/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/semver/bin/::**********
::res://node_modules/semver/classes/::**********
::res://node_modules/semver/functions/::**********
::res://node_modules/semver/internal/::**********
::res://node_modules/semver/ranges/::**********
::res://node_modules/simple-concat/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/simple-concat/test/::**********
::res://node_modules/simple-get/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/string_decoder/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/string_decoder/lib/::**********
::res://node_modules/strip-json-comments/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
readme.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/tar-fs/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/tar-fs/test/::**********
::res://node_modules/tar-fs/test/fixtures/::**********
::res://node_modules/tar-fs/test/fixtures/a/::**********
hello.txt::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/tar-fs/test/fixtures/b/::**********
::res://node_modules/tar-fs/test/fixtures/b/a/::**********
test.txt::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/tar-fs/test/fixtures/d/::**********
::res://node_modules/tar-fs/test/fixtures/d/sub-dir/::**********
::res://node_modules/tar-fs/test/fixtures/d/sub-files/::**********
::res://node_modules/tar-fs/test/fixtures/e/::**********
::res://node_modules/tar-fs/test/fixtures/e/directory/::**********
::res://node_modules/tar-stream/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/tunnel-agent/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/util-deprecate/::**********
History.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::1751303858::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/dist/::**********
::res://node_modules/web-streams-polyfill/dist/types/::**********
tsdoc-metadata.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/dist/types/ts3.6/::**********
::res://node_modules/web-streams-polyfill/es6/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/es2018/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/ponyfill/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/ponyfill/es6/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/web-streams-polyfill/ponyfill/es2018/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
::res://node_modules/wrappy/::**********
package.json::JSON::-1::**********::0::1::::<><><>0<>0<><>::
README.md::TextFile::-1::**********::0::1::::<><><>0<>0<><>::
::res://scenes/::1751302894
AssetGenerator.tscn::PackedScene/PackedScene::-1::1751302754::0::1::::<><><>0<>0<><>::res://scripts/AssetGenerator.gd
MainMenu.tscn::PackedScene::4070188531361412054::1751302889::0::1::::<><><>0<>0<><>::uid://dl1a2ateu1dmw::::res://scripts/MainMenu.gd
::res://scripts/::1751302891
AssetGenerator.gd::GDScript/GDScript::6733254567586422308::1751302726::0::1::::<>Control<><>0<>0<><>::
MainMenu.gd::GDScript/GDScript::7985061398699423882::1751302792::0::1::::<>Control<><>0<>0<><>::
ScenarioAPI.gd::GDScript/GDScript::8472668313112885933::1751302689::0::1::::<>Node<><>0<>0<><>::
